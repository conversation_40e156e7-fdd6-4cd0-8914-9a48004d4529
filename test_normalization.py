#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试货号标准化效果
"""

import json
import pandas as pd

def normalize_product_code(product_code):
    """标准化货号（去除空格，转换为小写）"""
    if pd.isna(product_code):
        return "unknown"
    return str(product_code).replace(' ', '').lower()

def test_normalization():
    """测试货号标准化"""
    print("🔍 测试货号标准化效果")
    print("=" * 50)
    
    # 读取数据
    with open('data.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 获取月度统计数据
    monthly_stats = data['monthly_statistics']
    
    # 统计原始货号
    original_products = {}
    for item in monthly_stats:
        product = item['货号']
        if product not in original_products:
            original_products[product] = 0
        original_products[product] += item['工程编号数量']
    
    print(f"📊 原始货号统计 (共 {len(original_products)} 个不同货号):")
    for product, count in sorted(original_products.items()):
        print(f"   • {product}: {count}")
    
    print("\n" + "=" * 50)
    
    # 标准化后统计
    normalized_products = {}
    for item in monthly_stats:
        product = normalize_product_code(item['货号'])
        if product not in normalized_products:
            normalized_products[product] = 0
        normalized_products[product] += item['工程编号数量']
    
    print(f"📊 标准化后货号统计 (共 {len(normalized_products)} 个不同货号):")
    for product, count in sorted(normalized_products.items()):
        print(f"   • {product}: {count}")
    
    print("\n" + "=" * 50)
    
    # 显示合并效果
    print("🔄 货号合并效果:")
    
    # 找出被合并的货号
    merged_groups = {}
    for original_product in original_products.keys():
        normalized = normalize_product_code(original_product)
        if normalized not in merged_groups:
            merged_groups[normalized] = []
        merged_groups[normalized].append(original_product)
    
    for normalized, originals in merged_groups.items():
        if len(originals) > 1:
            total_count = sum(original_products[orig] for orig in originals)
            print(f"   • '{normalized}' 合并了: {originals} (总计: {total_count})")
    
    print("\n" + "=" * 50)
    
    # 按客户分组统计
    print("👥 按客户分组的标准化货号统计:")
    
    customer_products = {}
    for item in monthly_stats:
        customer = item['客户简称']
        product = normalize_product_code(item['货号'])
        
        if customer not in customer_products:
            customer_products[customer] = {}
        if product not in customer_products[customer]:
            customer_products[customer][product] = 0
        customer_products[customer][product] += item['工程编号数量']
    
    for customer, products in customer_products.items():
        print(f"\n   {customer} 客户:")
        for product, count in sorted(products.items()):
            print(f"      • {product}: {count}")
    
    print("\n" + "=" * 50)
    print("✅ 货号标准化测试完成!")
    
    return {
        'original_count': len(original_products),
        'normalized_count': len(normalized_products),
        'reduction': len(original_products) - len(normalized_products)
    }

def test_chart_data_structure():
    """测试图表数据结构"""
    print("\n📈 测试图表数据结构")
    print("=" * 50)
    
    with open('data.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    monthly_stats = data['monthly_statistics']
    
    # 模拟前端的数据处理逻辑
    customer_data = {}
    for item in monthly_stats:
        customer = item['客户简称']
        month = item['年月']
        product = normalize_product_code(item['货号'])
        
        if customer not in customer_data:
            customer_data[customer] = {}
        if month not in customer_data[customer]:
            customer_data[customer][month] = 0
        customer_data[customer][month] += item['工程编号数量']
    
    # 显示每个客户的月度数据
    for customer, months in customer_data.items():
        print(f"\n{customer} 客户月度数据:")
        sorted_months = sorted(months.keys())
        print(f"   时间范围: {sorted_months[0]} 到 {sorted_months[-1]}")
        print(f"   数据点数: {len(sorted_months)}")
        print(f"   总工程编号数: {sum(months.values())}")
        
        # 显示前5个月的数据作为示例
        print("   前5个月数据:")
        for month in sorted_months[:5]:
            print(f"      {month}: {months[month]}")
    
    print("\n✅ 图表数据结构测试完成!")

if __name__ == "__main__":
    result = test_normalization()
    test_chart_data_structure()
    
    print(f"\n📋 总结:")
    print(f"   • 原始货号数量: {result['original_count']}")
    print(f"   • 标准化后货号数量: {result['normalized_count']}")
    print(f"   • 减少了 {result['reduction']} 个重复货号")
    print(f"   • 数据清理效果: {result['reduction']/result['original_count']*100:.1f}%")
