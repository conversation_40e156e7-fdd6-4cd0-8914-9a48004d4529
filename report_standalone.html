<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户数据统计报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        
        .card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #4facfe;
            margin-bottom: 10px;
        }
        
        .card-label {
            color: #666;
            font-size: 1.1em;
        }
        
        .filters {
            padding: 30px;
            background: white;
            border-bottom: 1px solid #eee;
        }
        
        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
        }
        
        .filter-group label {
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .filter-group select, .filter-group input {
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .filter-group select:focus, .filter-group input:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        .charts-container {
            padding: 30px;
        }
        
        .chart-section {
            margin-bottom: 50px;
        }
        
        .chart-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .chart-wrapper {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 30px;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin-bottom: 20px;
        }
        
        .data-table {
            margin-top: 30px;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .table-header {
            background: #4facfe;
            color: white;
            padding: 20px;
            font-size: 1.3em;
            font-weight: 600;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: transform 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .summary-cards {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                padding: 20px;
            }
            
            .filters {
                padding: 20px;
            }
            
            .filter-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 客户数据统计报告</h1>
            <p>数据分析与可视化仪表板</p>
        </div>
        
        <div class="summary-cards" id="summaryCards">
            <div class="loading">正在加载数据...</div>
        </div>
        
        <div class="filters">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="customerFilter">客户筛选</label>
                    <select id="customerFilter">
                        <option value="">全部客户</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="productFilter">货号筛选</label>
                    <select id="productFilter">
                        <option value="">全部货号</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="yearFilter">年份筛选</label>
                    <select id="yearFilter">
                        <option value="">全部年份</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="monthFilter">月份筛选</label>
                    <select id="monthFilter">
                        <option value="">全部月份</option>
                    </select>
                </div>
            </div>
            <div class="filter-row">
                <button class="btn" onclick="applyFilters()">应用筛选</button>
                <button class="btn" onclick="resetFilters()">重置筛选</button>
                <button class="btn" onclick="exportData()">导出数据</button>
            </div>
        </div>
        
        <div class="charts-container">
            <div class="chart-section">
                <h2 class="chart-title">📈 月度工程编号统计</h2>
                <div class="chart-wrapper">
                    <div class="chart-container">
                        <canvas id="monthlyChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="chart-section">
                <h2 class="chart-title">📊 年度工程编号统计</h2>
                <div class="chart-wrapper">
                    <div class="chart-container">
                        <canvas id="yearlyChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="chart-section">
                <h2 class="chart-title">🥧 客户分布</h2>
                <div class="chart-wrapper">
                    <div class="chart-container">
                        <canvas id="customerPieChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="data-table">
                <div class="table-header">📋 详细数据表</div>
                <div style="overflow-x: auto;">
                    <table id="dataTable">
                        <thead>
                            <tr>
                                <th>客户简称</th>
                                <th>货号</th>
                                <th>年月</th>
                                <th>工程编号数量</th>
                            </tr>
                        </thead>
                        <tbody id="tableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 嵌入的数据
        const rawData = {
  "summary": {
    "总客户数": 2,
    "总货号数": 28,
    "总工程编号数": 4764,
    "总订单数": 116,
    "数据记录总数": 4895,
    "数据时间范围": {
      "开始日期": "2020-01",
      "结束日期": "2025-08"
    }
  },
  "monthly_statistics": [
    {"客户简称": "APP", "货号": "Alb", "年月": "2020-01", "工程编号数量": 5},
    {"客户简称": "APP", "货号": "Alb", "年月": "2020-03", "工程编号数量": 7},
    {"客户简称": "APP", "货号": "Alb", "年月": "2020-05", "工程编号数量": 7},
    {"客户简称": "APP", "货号": "Alb", "年月": "2020-07", "工程编号数量": 1},
    {"客户简称": "APP", "货号": "Alb", "年月": "2020-09", "工程编号数量": 1},
    {"客户简称": "APP", "货号": "Alb", "年月": "2020-12", "工程编号数量": 1},
    {"客户简称": "APP", "货号": "Alb", "年月": "2022-08", "工程编号数量": 8},
    {"客户简称": "APP", "货号": "Alb", "年月": "2022-10", "工程编号数量": 6},
    {"客户简称": "APP", "货号": "Han", "年月": "2020-01", "工程编号数量": 19},
    {"客户简称": "APP", "货号": "Han", "年月": "2020-03", "工程编号数量": 33},
    {"客户简称": "APP", "货号": "Han", "年月": "2020-05", "工程编号数量": 21},
    {"客户简称": "APP", "货号": "Han", "年月": "2020-07", "工程编号数量": 1},
    {"客户简称": "APP", "货号": "Han", "年月": "2020-11", "工程编号数量": 6},
    {"客户简称": "APP", "货号": "Han", "年月": "2021-01", "工程编号数量": 1},
    {"客户简称": "APP", "货号": "Han", "年月": "2021-05", "工程编号数量": 1},
    {"客户简称": "APP", "货号": "Han", "年月": "2022-09", "工程编号数量": 1},
    {"客户简称": "APP", "货号": "MBT", "年月": "2020-01", "工程编号数量": 4},
    {"客户简称": "APP", "货号": "MBT", "年月": "2020-03", "工程编号数量": 4},
    {"客户简称": "APP", "货号": "MBT", "年月": "2020-05", "工程编号数量": 5},
    {"客户简称": "APP", "货号": "MBT", "年月": "2020-07", "工程编号数量": 2},
    {"客户简称": "APP", "货号": "MBT", "年月": "2020-09", "工程编号数量": 5},
    {"客户简称": "APP", "货号": "MBT", "年月": "2020-11", "工程编号数量": 2},
    {"客户简称": "APP", "货号": "MBT", "年月": "2020-12", "工程编号数量": 1},
    {"客户简称": "APP", "货号": "MBT", "年月": "2021-01", "工程编号数量": 2},
    {"客户简称": "APP", "货号": "MBT", "年月": "2021-04", "工程编号数量": 6},
    {"客户简称": "APP", "货号": "MBT", "年月": "2021-05", "工程编号数量": 2},
    {"客户简称": "APP", "货号": "MBT", "年月": "2021-11", "工程编号数量": 1},
    {"客户简称": "APP", "货号": "MBT", "年月": "2022-04", "工程编号数量": 1},
    {"客户简称": "APP", "货号": "MBT", "年月": "2022-07", "工程编号数量": 4},
    {"客户简称": "APP", "货号": "MBT", "年月": "2022-08", "工程编号数量": 1},
    {"客户简称": "APP", "货号": "MBT", "年月": "2022-11", "工程编号数量": 2},
    {"客户简称": "APP", "货号": "alb", "年月": "2020-07", "工程编号数量": 5},
    {"客户简称": "APP", "货号": "alb", "年月": "2020-09", "工程编号数量": 10},
    {"客户简称": "APP", "货号": "alb", "年月": "2020-11", "工程编号数量": 8},
    {"客户简称": "APP", "货号": "alb", "年月": "2020-12", "工程编号数量": 1},
    {"客户简称": "APP", "货号": "alb", "年月": "2021-01", "工程编号数量": 3},
    {"客户简称": "APP", "货号": "alb", "年月": "2021-04", "工程编号数量": 4},
    {"客户简称": "APP", "货号": "alb", "年月": "2021-07", "工程编号数量": 8},
    {"客户简称": "APP", "货号": "alb", "年月": "2021-09", "工程编号数量": 12},
    {"客户简称": "APP", "货号": "alb", "年月": "2021-10", "工程编号数量": 4},
    {"客户简称": "APP", "货号": "alb", "年月": "2021-11", "工程编号数量": 5},
    {"客户简称": "APP", "货号": "alb", "年月": "2022-01", "工程编号数量": 10},
    {"客户简称": "APP", "货号": "alb", "年月": "2022-04", "工程编号数量": 16},
    {"客户简称": "APP", "货号": "alb", "年月": "2022-06", "工程编号数量": 8},
    {"客户简称": "APP", "货号": "alb", "年月": "2022-07", "工程编号数量": 4},
    {"客户简称": "APP", "货号": "alb", "年月": "2022-08", "工程编号数量": 1},
    {"客户简称": "APP", "货号": "alb", "年月": "2022-09", "工程编号数量": 6},
    {"客户简称": "APP", "货号": "alb", "年月": "2022-11", "工程编号数量": 8},
    {"客户简称": "APP", "货号": "alb", "年月": "2023-01", "工程编号数量": 5},
    {"客户简称": "APP", "货号": "alb", "年月": "2023-03", "工程编号数量": 7},
    {"客户简称": "APP", "货号": "alb", "年月": "2023-05", "工程编号数量": 9},
    {"客户简称": "APP", "货号": "alb", "年月": "2023-06", "工程编号数量": 9},
    {"客户简称": "APP", "货号": "alb", "年月": "2023-07", "工程编号数量": 13},
    {"客户简称": "APP", "货号": "alb", "年月": "2023-09", "工程编号数量": 10},
    {"客户简称": "APP", "货号": "alb", "年月": "2023-12", "工程编号数量": 8},
    {"客户简称": "APP", "货号": "alb", "年月": "2024-01", "工程编号数量": 5},
    {"客户简称": "APP", "货号": "alb", "年月": "2024-04", "工程编号数量": 12},
    {"客户简称": "APP", "货号": "alb", "年月": "2024-06", "工程编号数量": 10},
    {"客户简称": "APP", "货号": "alb", "年月": "2024-07", "工程编号数量": 11},
    {"客户简称": "APP", "货号": "alb", "年月": "2024-08", "工程编号数量": 6},
    {"客户简称": "APP", "货号": "alb", "年月": "2024-11", "工程编号数量": 12},
    {"客户简称": "APP", "货号": "alb", "年月": "2025-01", "工程编号数量": 6},
    {"客户简称": "APP", "货号": "alb", "年月": "2025-03", "工程编号数量": 11},
    {"客户简称": "APP", "货号": "alb", "年月": "2025-04", "工程编号数量": 10},
    {"客户简称": "APP", "货号": "alb", "年月": "2025-05", "工程编号数量": 13},
    {"客户简称": "APP", "货号": "alb", "年月": "2025-06", "工程编号数量": 4},
    {"客户简称": "APP", "货号": "alb", "年月": "2025-08", "工程编号数量": 11},
    {"客户简称": "APP", "货号": "han", "年月": "2020-07", "工程编号数量": 21},
    {"客户简称": "APP", "货号": "han", "年月": "2020-09", "工程编号数量": 21},
    {"客户简称": "APP", "货号": "han", "年月": "2020-11", "工程编号数量": 30},
    {"客户简称": "APP", "货号": "han", "年月": "2020-12", "工程编号数量": 27},
    {"客户简称": "APP", "货号": "han", "年月": "2021-01", "工程编号数量": 28},
    {"客户简称": "APP", "货号": "han", "年月": "2021-04", "工程编号数量": 33},
    {"客户简称": "APP", "货号": "han", "年月": "2021-05", "工程编号数量": 36},
    {"客户简称": "APP", "货号": "han", "年月": "2021-07", "工程编号数量": 25},
    {"客户简称": "APP", "货号": "han", "年月": "2021-09", "工程编号数量": 24},
    {"客户简称": "APP", "货号": "han", "年月": "2021-10", "工程编号数量": 18},
    {"客户简称": "APP", "货号": "han", "年月": "2021-11", "工程编号数量": 11},
    {"客户简称": "APP", "货号": "han", "年月": "2022-01", "工程编号数量": 22},
    {"客户简称": "APP", "货号": "han", "年月": "2022-04", "工程编号数量": 26},
    {"客户简称": "APP", "货号": "han", "年月": "2022-06", "工程编号数量": 19},
    {"客户简称": "APP", "货号": "han", "年月": "2022-07", "工程编号数量": 22},
    {"客户简称": "APP", "货号": "han", "年月": "2022-08", "工程编号数量": 19},
    {"客户简称": "APP", "货号": "han", "年月": "2022-09", "工程编号数量": 25},
    {"客户简称": "APP", "货号": "han", "年月": "2022-10", "工程编号数量": 6},
    {"客户简称": "APP", "货号": "han", "年月": "2022-11", "工程编号数量": 22},
    {"客户简称": "APP", "货号": "han", "年月": "2023-01", "工程编号数量": 38},
    {"客户简称": "APP", "货号": "han", "年月": "2023-03", "工程编号数量": 33},
    {"客户简称": "APP", "货号": "han", "年月": "2023-05", "工程编号数量": 32},
    {"客户简称": "APP", "货号": "han", "年月": "2023-06", "工程编号数量": 22},
    {"客户简称": "APP", "货号": "han", "年月": "2023-07", "工程编号数量": 20},
    {"客户简称": "APP", "货号": "han", "年月": "2023-09", "工程编号数量": 30},
    {"客户简称": "APP", "货号": "han", "年月": "2023-12", "工程编号数量": 27},
    {"客户简称": "APP", "货号": "han", "年月": "2024-01", "工程编号数量": 21},
    {"客户简称": "APP", "货号": "han", "年月": "2024-04", "工程编号数量": 33},
    {"客户简称": "APP", "货号": "han", "年月": "2024-06", "工程编号数量": 30},
    {"客户简称": "APP", "货号": "han", "年月": "2024-07", "工程编号数量": 17},
    {"客户简称": "APP", "货号": "han", "年月": "2024-08", "工程编号数量": 24},
    {"客户简称": "APP", "货号": "han", "年月": "2024-11", "工程编号数量": 35},
    {"客户简称": "APP", "货号": "han", "年月": "2025-01", "工程编号数量": 31},
    {"客户简称": "APP", "货号": "han", "年月": "2025-03", "工程编号数量": 21},
    {"客户简称": "APP", "货号": "han", "年月": "2025-04", "工程编号数量": 21},
    {"客户简称": "APP", "货号": "han", "年月": "2025-05", "工程编号数量": 29},
    {"客户简称": "APP", "货号": "han", "年月": "2025-06", "工程编号数量": 29},
    {"客户简称": "APP", "货号": "han", "年月": "2025-08", "工程编号数量": 26},
    {"客户简称": "GB", "货号": "ERF", "年月": "2020-06", "工程编号数量": 2},
    {"客户简称": "GB", "货号": "ERF", "年月": "2020-07", "工程编号数量": 6},
    {"客户简称": "GB", "货号": "ERF", "年月": "2020-08", "工程编号数量": 2},
    {"客户简称": "GB", "货号": "ERF", "年月": "2020-09", "工程编号数量": 5},
    {"客户简称": "GB", "货号": "ERF", "年月": "2020-10", "工程编号数量": 2},
    {"客户简称": "GB", "货号": "ERF", "年月": "2020-11", "工程编号数量": 8},
    {"客户简称": "GB", "货号": "ERF", "年月": "2021-02", "工程编号数量": 5},
    {"客户简称": "GB", "货号": "ERF", "年月": "2021-03", "工程编号数量": 6},
    {"客户简称": "GB", "货号": "ERF", "年月": "2021-04", "工程编号数量": 4},
    {"客户简称": "GB", "货号": "ERF", "年月": "2021-05", "工程编号数量": 3},
    {"客户简称": "GB", "货号": "ERF", "年月": "2021-06", "工程编号数量": 3},
    {"客户简称": "GB", "货号": "ERF", "年月": "2021-07", "工程编号数量": 8},
    {"客户简称": "GB", "货号": "ERF", "年月": "2021-08", "工程编号数量": 2},
    {"客户简称": "GB", "货号": "ERF", "年月": "2021-09", "工程编号数量": 2},
    {"客户简称": "GB", "货号": "ERF", "年月": "2021-11", "工程编号数量": 2},
    {"客户简称": "GB", "货号": "ERF", "年月": "2022-02", "工程编号数量": 5},
    {"客户简称": "GB", "货号": "ERF", "年月": "2022-04", "工程编号数量": 8},
    {"客户简称": "GB", "货号": "ERF", "年月": "2022-05", "工程编号数量": 3},
    {"客户简称": "GB", "货号": "ERF", "年月": "2022-06", "工程编号数量": 6},
    {"客户简称": "GB", "货号": "ERF", "年月": "2022-07", "工程编号数量": 8},
    {"客户简称": "GB", "货号": "ERF", "年月": "2022-08", "工程编号数量": 3},
    {"客户简称": "GB", "货号": "ERF", "年月": "2022-11", "工程编号数量": 12},
    {"客户简称": "GB", "货号": "ERF", "年月": "2023-01", "工程编号数量": 11},
    {"客户简称": "GB", "货号": "ERF", "年月": "2023-03", "工程编号数量": 4},
    {"客户简称": "GB", "货号": "ERF", "年月": "2023-04", "工程编号数量": 3},
    {"客户简称": "GB", "货号": "ERF", "年月": "2023-05", "工程编号数量": 2},
    {"客户简称": "GB", "货号": "ERF", "年月": "2023-06", "工程编号数量": 7},
    {"客户简称": "GB", "货号": "ERF", "年月": "2023-07", "工程编号数量": 1},
    {"客户简称": "GB", "货号": "ERF", "年月": "2023-08", "工程编号数量": 1},
    {"客户简称": "GB", "货号": "ERF", "年月": "2023-09", "工程编号数量": 5},
    {"客户简称": "GB", "货号": "ERF", "年月": "2023-10", "工程编号数量": 2},
    {"客户简称": "GB", "货号": "ERF", "年月": "2024-04", "工程编号数量": 2}
  ],
  "yearly_statistics": [
    {"客户简称": "APP", "货号": "Alb", "年份": 2020, "工程编号数量": 21},
    {"客户简称": "APP", "货号": "Alb", "年份": 2022, "工程编号数量": 14},
    {"客户简称": "APP", "货号": "Han", "年份": 2020, "工程编号数量": 78},
    {"客户简称": "APP", "货号": "Han", "年份": 2021, "工程编号数量": 2},
    {"客户简称": "APP", "货号": "Han", "年份": 2022, "工程编号数量": 1},
    {"客户简称": "APP", "货号": "MBT", "年份": 2020, "工程编号数量": 23},
    {"客户简称": "APP", "货号": "MBT", "年份": 2021, "工程编号数量": 11},
    {"客户简称": "APP", "货号": "MBT", "年份": 2022, "工程编号数量": 8},
    {"客户简称": "APP", "货号": "alb", "年份": 2020, "工程编号数量": 23},
    {"客户简称": "APP", "货号": "alb", "年份": 2021, "工程编号数量": 36},
    {"客户简称": "APP", "货号": "alb", "年份": 2022, "工程编号数量": 53},
    {"客户简称": "APP", "货号": "alb", "年份": 2023, "工程编号数量": 52},
    {"客户简称": "APP", "货号": "alb", "年份": 2024, "工程编号数量": 44},
    {"客户简称": "APP", "货号": "alb", "年份": 2025, "工程编号数量": 45},
    {"客户简称": "APP", "货号": "han", "年份": 2020, "工程编号数量": 99},
    {"客户简称": "APP", "货号": "han", "年份": 2021, "工程编号数量": 175},
    {"客户简称": "APP", "货号": "han", "年份": 2022, "工程编号数量": 161},
    {"客户简称": "APP", "货号": "han", "年份": 2023, "工程编号数量": 202},
    {"客户简称": "APP", "货号": "han", "年份": 2024, "工程编号数量": 160},
    {"客户简称": "APP", "货号": "han", "年份": 2025, "工程编号数量": 157},
    {"客户简称": "GB", "货号": "ERF", "年份": 2020, "工程编号数量": 25},
    {"客户简称": "GB", "货号": "ERF", "年份": 2021, "工程编号数量": 33},
    {"客户简称": "GB", "货号": "ERF", "年份": 2022, "工程编号数量": 45},
    {"客户简称": "GB", "货号": "ERF", "年份": 2023, "工程编号数量": 36},
    {"客户简称": "GB", "货号": "ERF", "年份": 2024, "工程编号数量": 2}
  ],
  "metadata": {
    "generated_time": "2025-09-09T09:41:39.090196",
    "columns": {
      "customer": "客户简称",
      "date": "船期",
      "order": "订单编号",
      "project": "工程编号",
      "product": "货号"
    }
  }
};

        let filteredData = null;
        let charts = {};

        // 初始化页面
        function initializePage() {
            initializeFilters();
            updateSummaryCards();
            createCharts();
            updateTable();
        }

        // 初始化筛选器
        function initializeFilters() {
            const customers = [...new Set(rawData.monthly_statistics.map(item => item.客户简称))];
            const products = [...new Set(rawData.monthly_statistics.map(item => item.货号))];
            const years = [...new Set(rawData.monthly_statistics.map(item => item.年月.split('-')[0]))];
            const months = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'];

            populateSelect('customerFilter', customers);
            populateSelect('productFilter', products);
            populateSelect('yearFilter', years);
            populateSelect('monthFilter', months);
        }

        function populateSelect(selectId, options) {
            const select = document.getElementById(selectId);
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option;
                optionElement.textContent = option;
                select.appendChild(optionElement);
            });
        }

        // 更新汇总卡片
        function updateSummaryCards() {
            const summary = rawData.summary;
            const cardsHtml = `
                <div class="card">
                    <div class="card-number">${summary.总客户数}</div>
                    <div class="card-label">总客户数</div>
                </div>
                <div class="card">
                    <div class="card-number">${summary.总货号数}</div>
                    <div class="card-label">总货号数</div>
                </div>
                <div class="card">
                    <div class="card-number">${summary.总工程编号数}</div>
                    <div class="card-label">总工程编号数</div>
                </div>
                <div class="card">
                    <div class="card-number">${summary.总订单数}</div>
                    <div class="card-label">总订单数</div>
                </div>
                <div class="card">
                    <div class="card-number">${summary.数据记录总数}</div>
                    <div class="card-label">数据记录总数</div>
                </div>
            `;
            document.getElementById('summaryCards').innerHTML = cardsHtml;
        }

        // 应用筛选
        function applyFilters() {
            const customerFilter = document.getElementById('customerFilter').value;
            const productFilter = document.getElementById('productFilter').value;
            const yearFilter = document.getElementById('yearFilter').value;
            const monthFilter = document.getElementById('monthFilter').value;

            filteredData = rawData.monthly_statistics.filter(item => {
                return (!customerFilter || item.客户简称 === customerFilter) &&
                       (!productFilter || item.货号 === productFilter) &&
                       (!yearFilter || item.年月.startsWith(yearFilter)) &&
                       (!monthFilter || item.年月.endsWith(monthFilter));
            });

            updateCharts();
            updateTable();
        }

        // 重置筛选
        function resetFilters() {
            document.getElementById('customerFilter').value = '';
            document.getElementById('productFilter').value = '';
            document.getElementById('yearFilter').value = '';
            document.getElementById('monthFilter').value = '';
            filteredData = null;
            updateCharts();
            updateTable();
        }

        // 创建图表
        function createCharts() {
            createMonthlyChart();
            createYearlyChart();
            createCustomerPieChart();
        }

        // 标准化货号（去除空格，转换为小写）
        function normalizeProductCode(productCode) {
            return productCode.replace(/\s+/g, '').toLowerCase();
        }

        // 处理数据，标准化货号
        function processDataWithNormalizedProducts(data) {
            return data.map(item => ({
                ...item,
                货号: normalizeProductCode(item.货号)
            }));
        }

        // 创建月度图表
        function createMonthlyChart() {
            const ctx = document.getElementById('monthlyChart').getContext('2d');
            const rawData = processDataWithNormalizedProducts(getCurrentData());

            // 按客户和年月分组统计
            const customerData = {};
            rawData.forEach(item => {
                const customer = item.客户简称;
                const month = item.年月;

                if (!customerData[customer]) {
                    customerData[customer] = {};
                }
                if (!customerData[customer][month]) {
                    customerData[customer][month] = 0;
                }
                customerData[customer][month] += item.工程编号数量;
            });

            // 获取所有月份并排序
            const allMonths = new Set();
            Object.values(customerData).forEach(customerMonths => {
                Object.keys(customerMonths).forEach(month => allMonths.add(month));
            });
            const sortedMonths = Array.from(allMonths).sort();

            // 为每个客户创建数据集
            const datasets = [];
            const colors = ['#4facfe', '#00f2fe', '#43e97b', '#38f9d7', '#ffecd2', '#fcb69f'];
            let colorIndex = 0;

            Object.keys(customerData).forEach(customer => {
                const customerMonths = customerData[customer];
                const values = sortedMonths.map(month => customerMonths[month] || 0);

                datasets.push({
                    label: `${customer} - 工程编号数量`,
                    data: values,
                    borderColor: colors[colorIndex % colors.length],
                    backgroundColor: colors[colorIndex % colors.length] + '20',
                    borderWidth: 3,
                    fill: false,
                    tension: 0.4,
                    pointBackgroundColor: colors[colorIndex % colors.length],
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                });
                colorIndex++;
            });

            if (charts.monthly) {
                charts.monthly.destroy();
            }

            charts.monthly = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: sortedMonths,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '年月'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: '工程编号数量'
                            },
                            beginAtZero: true
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    }
                }
            });
        }

        // 创建年度图表
        function createYearlyChart() {
            const ctx = document.getElementById('yearlyChart').getContext('2d');
            const rawYearlyData = processDataWithNormalizedProducts(getCurrentYearlyData());

            // 按客户和年份分组统计
            const customerData = {};
            rawYearlyData.forEach(item => {
                const customer = item.客户简称;
                const year = item.年份.toString();

                if (!customerData[customer]) {
                    customerData[customer] = {};
                }
                if (!customerData[customer][year]) {
                    customerData[customer][year] = 0;
                }
                customerData[customer][year] += item.工程编号数量;
            });

            // 获取所有年份并排序
            const allYears = new Set();
            Object.values(customerData).forEach(customerYears => {
                Object.keys(customerYears).forEach(year => allYears.add(year));
            });
            const sortedYears = Array.from(allYears).sort();

            // 为每个客户创建数据集
            const datasets = [];
            const colors = ['#4facfe', '#00f2fe', '#43e97b', '#38f9d7', '#ffecd2', '#fcb69f'];
            let colorIndex = 0;

            Object.keys(customerData).forEach(customer => {
                const customerYears = customerData[customer];
                const values = sortedYears.map(year => customerYears[year] || 0);

                datasets.push({
                    label: `${customer} - 工程编号数量`,
                    data: values,
                    backgroundColor: colors[colorIndex % colors.length] + 'CC',
                    borderColor: colors[colorIndex % colors.length],
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                });
                colorIndex++;
            });

            if (charts.yearly) {
                charts.yearly.destroy();
            }

            charts.yearly = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: sortedYears,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '年份'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: '工程编号数量'
                            },
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 创建客户饼图
        function createCustomerPieChart() {
            const ctx = document.getElementById('customerPieChart').getContext('2d');
            const rawData = processDataWithNormalizedProducts(getCurrentData());

            // 按客户分组统计
            const customerData = {};
            rawData.forEach(item => {
                const key = item.客户简称;
                if (!customerData[key]) {
                    customerData[key] = 0;
                }
                customerData[key] += item.工程编号数量;
            });

            const labels = Object.keys(customerData);
            const values = Object.values(customerData);
            const colors = generateColors(labels.length);

            if (charts.customerPie) {
                charts.customerPie.destroy();
            }

            charts.customerPie = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        backgroundColor: colors,
                        borderColor: '#fff',
                        borderWidth: 3,
                        hoverOffset: 10
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed * 100) / total).toFixed(1);
                                    return `${context.label}: ${context.parsed} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // 生成颜色
        function generateColors(count) {
            const colors = [
                '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
                '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3',
                '#d299c2', '#fef9d7', '#667eea', '#764ba2'
            ];

            const result = [];
            for (let i = 0; i < count; i++) {
                result.push(colors[i % colors.length]);
            }
            return result;
        }

        // 获取当前数据（考虑筛选）
        function getCurrentData() {
            return filteredData || rawData.monthly_statistics;
        }

        // 获取当前年度数据（考虑筛选）
        function getCurrentYearlyData() {
            if (!filteredData) {
                return rawData.yearly_statistics;
            }

            // 从月度筛选数据生成年度数据
            const yearlyData = {};
            filteredData.forEach(item => {
                const year = parseInt(item.年月.split('-')[0]);
                const normalizedProduct = normalizeProductCode(item.货号);
                const key = `${item.客户简称}-${normalizedProduct}-${year}`;
                if (!yearlyData[key]) {
                    yearlyData[key] = {
                        客户简称: item.客户简称,
                        货号: normalizedProduct,
                        年份: year,
                        工程编号数量: 0
                    };
                }
                yearlyData[key].工程编号数量 += item.工程编号数量;
            });

            return Object.values(yearlyData);
        }

        // 更新图表
        function updateCharts() {
            createMonthlyChart();
            createYearlyChart();
            createCustomerPieChart();
        }

        // 更新表格
        function updateTable() {
            const rawData = processDataWithNormalizedProducts(getCurrentData());
            const tbody = document.getElementById('tableBody');

            tbody.innerHTML = rawData.map(item => `
                <tr>
                    <td>${item.客户简称}</td>
                    <td>${item.货号}</td>
                    <td>${item.年月}</td>
                    <td>${item.工程编号数量}</td>
                </tr>
            `).join('');
        }

        // 导出数据
        function exportData() {
            const rawData = processDataWithNormalizedProducts(getCurrentData());
            const csv = convertToCSV(rawData);
            downloadCSV(csv, 'customer_data_report.csv');
        }

        function convertToCSV(data) {
            const headers = ['客户简称', '货号', '年月', '工程编号数量'];
            const csvContent = [
                headers.join(','),
                ...data.map(row => headers.map(header => `"${row[header]}"`).join(','))
            ].join('\n');
            return csvContent;
        }

        function downloadCSV(csv, filename) {
            const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
