#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键生成客户数据统计报告
"""

import subprocess
import sys
import os
import webbrowser
from pathlib import Path

def install_requirements():
    """安装必要的依赖包"""
    required_packages = ['pandas', 'openpyxl']
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"正在安装 {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✓ {package} 安装完成")

def check_files():
    """检查必要文件是否存在"""
    files_to_check = ['GB_APP.xlsx', 'data_processor.py', 'report.html']
    missing_files = []
    
    for file in files_to_check:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            print(f"✓ {file} 存在")
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    return True

def generate_data():
    """生成数据文件"""
    print("正在处理Excel数据...")
    try:
        result = subprocess.run([sys.executable, 'data_processor.py'], 
                              capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✓ 数据处理完成")
            print(result.stdout)
            return True
        else:
            print("❌ 数据处理失败:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 运行数据处理脚本失败: {e}")
        return False

def open_report():
    """打开报告页面"""
    report_path = Path('report.html').absolute()
    report_url = f"file:///{report_path.as_posix()}"
    
    print(f"正在打开报告页面: {report_url}")
    try:
        webbrowser.open(report_url)
        print("✓ 报告页面已在浏览器中打开")
        return True
    except Exception as e:
        print(f"❌ 打开浏览器失败: {e}")
        print(f"请手动打开: {report_path}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 客户数据统计报告生成器")
    print("=" * 50)
    
    # 1. 安装依赖
    print("\n📦 检查并安装依赖包...")
    install_requirements()
    
    # 2. 检查文件
    print("\n📁 检查必要文件...")
    if not check_files():
        print("\n❌ 请确保所有必要文件都存在后重新运行")
        return
    
    # 3. 生成数据
    print("\n📊 生成统计数据...")
    if not generate_data():
        print("\n❌ 数据生成失败，请检查Excel文件格式")
        return
    
    # 4. 打开报告
    print("\n🌐 打开报告页面...")
    open_report()
    
    print("\n" + "=" * 50)
    print("✅ 报告生成完成！")
    print("=" * 50)
    print("\n📋 功能说明:")
    print("• 汇总统计卡片显示总体数据概况")
    print("• 筛选器支持按客户、货号、年份、月份过滤")
    print("• 月度趋势图显示工程编号数量变化")
    print("• 年度柱状图显示年度统计对比")
    print("• 客户分布饼图显示客户占比")
    print("• 详细数据表支持查看具体数据")
    print("• 支持导出筛选后的数据为CSV格式")
    print("\n💡 提示:")
    print("• 使用筛选器可以查看特定条件下的数据")
    print("• 点击'应用筛选'按钮更新图表和表格")
    print("• 点击'重置筛选'按钮恢复全部数据")
    print("• 点击'导出数据'按钮下载当前显示的数据")

if __name__ == "__main__":
    main()
