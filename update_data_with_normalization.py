#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新数据处理脚本，应用货号标准化
"""

import pandas as pd
import json
from datetime import datetime
import os

def normalize_product_code(product_code):
    """标准化货号（去除空格，转换为小写）"""
    if pd.isna(product_code):
        return "unknown"
    return str(product_code).replace(' ', '').lower()

def read_and_process_excel(file_path):
    """读取并处理Excel文件"""
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        print(f"成功读取数据，共 {len(df)} 行")
        
        # 自动检测列名
        columns = df.columns.tolist()
        customer_col = '客户简称'
        date_col = '船期'
        order_col = '订单编号'
        project_col = '工程编号'
        product_col = '货号'
        
        # 处理日期列
        df[date_col] = pd.to_datetime(df[date_col])
        df['年份'] = df[date_col].dt.year
        df['月份'] = df[date_col].dt.month
        df['年月'] = df[date_col].dt.strftime('%Y-%m')
        
        # 标准化货号
        df['原始货号'] = df[product_col]
        df[product_col] = df[product_col].apply(normalize_product_code)
        
        print(f"✅ 货号标准化完成")
        print(f"   原始货号数量: {df['原始货号'].nunique()}")
        print(f"   标准化后货号数量: {df[product_col].nunique()}")
        
        return df, customer_col, date_col, order_col, project_col, product_col
        
    except Exception as e:
        print(f"处理Excel文件失败: {e}")
        return None, None, None, None, None, None

def generate_statistics(df, customer_col, product_col, project_col, order_col):
    """生成统计数据"""
    try:
        # 月度统计
        monthly_stats = df.groupby([customer_col, product_col, '年月'])[project_col].nunique().reset_index()
        monthly_stats.columns = ['客户简称', '货号', '年月', '工程编号数量']
        
        # 年度统计
        yearly_stats = df.groupby([customer_col, product_col, '年份'])[project_col].nunique().reset_index()
        yearly_stats.columns = ['客户简称', '货号', '年份', '工程编号数量']
        
        # 汇总统计
        summary = {
            '总客户数': df[customer_col].nunique(),
            '总货号数': df[product_col].nunique(),
            '原始货号数': df['原始货号'].nunique(),
            '总工程编号数': df[project_col].nunique(),
            '总订单数': df[order_col].nunique(),
            '数据记录总数': len(df),
            '数据时间范围': {
                '开始日期': df['年月'].min(),
                '结束日期': df['年月'].max()
            },
            '货号标准化效果': {
                '原始货号数量': int(df['原始货号'].nunique()),
                '标准化后货号数量': int(df[product_col].nunique()),
                '减少数量': int(df['原始货号'].nunique() - df[product_col].nunique()),
                '减少比例': f"{((df['原始货号'].nunique() - df[product_col].nunique()) / df['原始货号'].nunique() * 100):.1f}%"
            }
        }
        
        return monthly_stats, yearly_stats, summary
        
    except Exception as e:
        print(f"生成统计数据失败: {e}")
        return None, None, None

def generate_product_mapping(df):
    """生成货号映射表"""
    mapping = {}
    for _, row in df[['原始货号', '货号']].drop_duplicates().iterrows():
        original = row['原始货号']
        normalized = row['货号']
        if normalized not in mapping:
            mapping[normalized] = []
        if original not in mapping[normalized]:
            mapping[normalized].append(original)
    
    return mapping

def main():
    """主函数"""
    print("🔄 更新数据处理 - 应用货号标准化")
    print("=" * 50)
    
    excel_file = "GB_APP.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ 文件 {excel_file} 不存在")
        return
    
    # 读取和处理数据
    df, customer_col, date_col, order_col, project_col, product_col = read_and_process_excel(excel_file)
    if df is None:
        return
    
    # 生成统计数据
    monthly_stats, yearly_stats, summary = generate_statistics(df, customer_col, product_col, project_col, order_col)
    if monthly_stats is None:
        return
    
    # 生成货号映射表
    product_mapping = generate_product_mapping(df)
    
    # 准备JSON数据
    json_data = {
        'summary': summary,
        'monthly_statistics': monthly_stats.to_dict('records'),
        'yearly_statistics': yearly_stats.to_dict('records'),
        'raw_data': df.to_dict('records'),
        'product_mapping': product_mapping,
        'metadata': {
            'generated_time': datetime.now().isoformat(),
            'normalization_applied': True,
            'columns': {
                'customer': customer_col,
                'date': date_col,
                'order': order_col,
                'project': project_col,
                'product': product_col
            }
        }
    }
    
    # 保存JSON数据
    with open('data_normalized.json', 'w', encoding='utf-8') as f:
        json.dump(json_data, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n✅ 数据处理完成!")
    print(f"📊 统计结果:")
    print(f"   • 月度统计记录数: {len(monthly_stats)}")
    print(f"   • 年度统计记录数: {len(yearly_stats)}")
    print(f"   • 数据已保存到 data_normalized.json")
    
    print(f"\n📈 汇总信息:")
    for key, value in summary.items():
        if key != '货号标准化效果':
            print(f"   • {key}: {value}")
    
    print(f"\n🔄 货号标准化效果:")
    normalization_effect = summary['货号标准化效果']
    for key, value in normalization_effect.items():
        print(f"   • {key}: {value}")
    
    print(f"\n📋 货号映射示例:")
    count = 0
    for normalized, originals in product_mapping.items():
        if len(originals) > 1:
            print(f"   • '{normalized}' ← {originals}")
            count += 1
            if count >= 5:  # 只显示前5个示例
                break
    
    print(f"\n🎯 按客户统计 (标准化后):")
    customer_stats = monthly_stats.groupby('客户简称')['工程编号数量'].sum().to_dict()
    total = sum(customer_stats.values())
    for customer, count in customer_stats.items():
        percentage = (count / total) * 100
        print(f"   • {customer}: {count} ({percentage:.1f}%)")

if __name__ == "__main__":
    main()
