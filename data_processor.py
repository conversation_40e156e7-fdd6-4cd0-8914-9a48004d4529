#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户数据统计处理脚本
读取Excel文件，进行数据统计分析，生成JSON数据用于HTML报告
"""

import pandas as pd
import json
from datetime import datetime
import os

def read_excel_data(file_path):
    """读取Excel文件数据"""
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        print(f"成功读取数据，共 {len(df)} 行")
        print("数据列名:", df.columns.tolist())
        print("\n前5行数据:")
        print(df.head())
        return df
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return None

def process_date_column(df, date_column):
    """处理日期列，提取年份和月份"""
    try:
        # 转换为日期格式
        df[date_column] = pd.to_datetime(df[date_column])
        df['年份'] = df[date_column].dt.year
        df['月份'] = df[date_column].dt.month
        df['年月'] = df[date_column].dt.strftime('%Y-%m')
        return df
    except Exception as e:
        print(f"处理日期列失败: {e}")
        return df

def monthly_statistics(df, customer_col, product_col, project_col):
    """按客户、货号、月份分组统计工程编号数量"""
    try:
        monthly_stats = df.groupby([customer_col, product_col, '年月'])[project_col].nunique().reset_index()
        monthly_stats.columns = ['客户简称', '货号', '年月', '工程编号数量']
        return monthly_stats
    except Exception as e:
        print(f"月度统计失败: {e}")
        return pd.DataFrame()

def yearly_statistics(df, customer_col, product_col, project_col):
    """按客户、货号、年份分组统计工程编号数量"""
    try:
        yearly_stats = df.groupby([customer_col, product_col, '年份'])[project_col].nunique().reset_index()
        yearly_stats.columns = ['客户简称', '货号', '年份', '工程编号数量']
        return yearly_stats
    except Exception as e:
        print(f"年度统计失败: {e}")
        return pd.DataFrame()

def generate_summary_stats(df, customer_col, product_col, project_col, order_col):
    """生成汇总统计信息"""
    try:
        summary = {
            '总客户数': df[customer_col].nunique(),
            '总货号数': df[product_col].nunique(),
            '总工程编号数': df[project_col].nunique(),
            '总订单数': df[order_col].nunique(),
            '数据记录总数': len(df),
            '数据时间范围': {
                '开始日期': df['年月'].min(),
                '结束日期': df['年月'].max()
            }
        }
        return summary
    except Exception as e:
        print(f"生成汇总统计失败: {e}")
        return {}

def main():
    # Excel文件路径
    excel_file = "GB_APP.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"文件 {excel_file} 不存在")
        return
    
    # 读取数据
    df = read_excel_data(excel_file)
    if df is None:
        return
    
    # 自动检测列名（根据常见的列名模式）
    columns = df.columns.tolist()
    print(f"\n检测到的列名: {columns}")
    
    # 尝试自动匹配列名
    customer_col = None
    date_col = None
    order_col = None
    project_col = None
    product_col = None
    
    for col in columns:
        col_lower = col.lower()
        if '客户' in col or 'customer' in col_lower:
            customer_col = col
        elif '船期' in col or '日期' in col or 'date' in col_lower:
            date_col = col
        elif '订单' in col or 'order' in col_lower:
            order_col = col
        elif '工程' in col or 'project' in col_lower:
            project_col = col
        elif '货号' in col or 'product' in col_lower or '产品' in col:
            product_col = col
    
    print(f"\n自动匹配的列:")
    print(f"客户列: {customer_col}")
    print(f"日期列: {date_col}")
    print(f"订单列: {order_col}")
    print(f"工程列: {project_col}")
    print(f"货号列: {product_col}")
    
    if not all([customer_col, date_col, project_col, product_col]):
        print("无法自动匹配所有必需的列，请检查数据格式")
        return
    
    # 处理日期列
    df = process_date_column(df, date_col)
    
    # 进行统计分析
    monthly_stats = monthly_statistics(df, customer_col, product_col, project_col)
    yearly_stats = yearly_statistics(df, customer_col, product_col, project_col)
    summary_stats = generate_summary_stats(df, customer_col, product_col, project_col, order_col)
    
    # 准备JSON数据
    json_data = {
        'summary': summary_stats,
        'monthly_statistics': monthly_stats.to_dict('records'),
        'yearly_statistics': yearly_stats.to_dict('records'),
        'raw_data': df.to_dict('records'),
        'metadata': {
            'generated_time': datetime.now().isoformat(),
            'columns': {
                'customer': customer_col,
                'date': date_col,
                'order': order_col,
                'project': project_col,
                'product': product_col
            }
        }
    }
    
    # 保存JSON数据
    with open('data.json', 'w', encoding='utf-8') as f:
        json.dump(json_data, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n统计完成!")
    print(f"月度统计记录数: {len(monthly_stats)}")
    print(f"年度统计记录数: {len(yearly_stats)}")
    print(f"数据已保存到 data.json")
    
    # 显示部分统计结果
    print("\n=== 月度统计示例 ===")
    print(monthly_stats.head(10))
    
    print("\n=== 年度统计示例 ===")
    print(yearly_stats.head(10))
    
    print("\n=== 汇总信息 ===")
    for key, value in summary_stats.items():
        print(f"{key}: {value}")

if __name__ == "__main__":
    main()
