#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试报告页面功能
"""

import json
import os
from http.server import HTTPServer, SimpleHTTPRequestHandler
import webbrowser
import threading
import time

def test_data_integrity():
    """测试数据完整性"""
    print("🔍 测试数据完整性...")
    
    if not os.path.exists('data.json'):
        print("❌ data.json 文件不存在")
        return False
    
    try:
        with open('data.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 检查必要的数据结构
        required_keys = ['summary', 'monthly_statistics', 'yearly_statistics', 'raw_data', 'metadata']
        for key in required_keys:
            if key not in data:
                print(f"❌ 缺少数据字段: {key}")
                return False
            print(f"✓ {key} 字段存在")
        
        # 检查数据数量
        print(f"✓ 月度统计记录: {len(data['monthly_statistics'])}")
        print(f"✓ 年度统计记录: {len(data['yearly_statistics'])}")
        print(f"✓ 原始数据记录: {len(data['raw_data'])}")
        
        # 检查汇总信息
        summary = data['summary']
        print(f"✓ 总客户数: {summary['总客户数']}")
        print(f"✓ 总货号数: {summary['总货号数']}")
        print(f"✓ 总工程编号数: {summary['总工程编号数']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据文件读取失败: {e}")
        return False

def start_local_server(port=8000):
    """启动本地HTTP服务器"""
    class CustomHandler(SimpleHTTPRequestHandler):
        def end_headers(self):
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            super().end_headers()
    
    try:
        server = HTTPServer(('localhost', port), CustomHandler)
        print(f"🌐 启动本地服务器: http://localhost:{port}")
        
        # 在新线程中启动服务器
        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.daemon = True
        server_thread.start()
        
        return server, port
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
        return None, None

def test_report_page():
    """测试报告页面"""
    print("\n🌐 测试报告页面...")
    
    # 检查HTML文件
    if not os.path.exists('report.html'):
        print("❌ report.html 文件不存在")
        return False
    
    print("✓ report.html 文件存在")
    
    # 启动本地服务器
    server, port = start_local_server()
    if not server:
        return False
    
    # 打开浏览器
    url = f"http://localhost:{port}/report.html"
    print(f"📱 在浏览器中打开: {url}")
    
    try:
        webbrowser.open(url)
        print("✓ 浏览器已打开")
        
        # 等待用户查看
        print("\n" + "="*50)
        print("🎯 请在浏览器中查看报告页面功能:")
        print("• 检查汇总卡片是否显示正确")
        print("• 测试筛选功能是否正常")
        print("• 查看图表是否正确渲染")
        print("• 测试数据表格是否显示")
        print("• 尝试导出功能")
        print("="*50)
        
        input("\n按回车键继续...")
        
        # 关闭服务器
        server.shutdown()
        print("✓ 服务器已关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ 打开浏览器失败: {e}")
        server.shutdown()
        return False

def generate_test_summary():
    """生成测试总结"""
    print("\n" + "="*60)
    print("📊 客户数据统计报告系统 - 功能测试总结")
    print("="*60)
    
    if os.path.exists('data.json'):
        with open('data.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        summary = data['summary']
        print(f"📈 数据概况:")
        print(f"   • 数据时间范围: {summary['数据时间范围']['开始日期']} 至 {summary['数据时间范围']['结束日期']}")
        print(f"   • 总客户数: {summary['总客户数']}")
        print(f"   • 总货号数: {summary['总货号数']}")
        print(f"   • 总工程编号数: {summary['总工程编号数']}")
        print(f"   • 总订单数: {summary['总订单数']}")
        print(f"   • 数据记录总数: {summary['数据记录总数']}")
        
        print(f"\n📊 统计分析:")
        print(f"   • 月度统计记录: {len(data['monthly_statistics'])} 条")
        print(f"   • 年度统计记录: {len(data['yearly_statistics'])} 条")
        
        # 分析客户分布
        customers = {}
        for record in data['monthly_statistics']:
            customer = record['客户简称']
            if customer not in customers:
                customers[customer] = 0
            customers[customer] += record['工程编号数量']
        
        print(f"\n👥 客户工程编号分布:")
        for customer, count in customers.items():
            percentage = (count / summary['总工程编号数']) * 100
            print(f"   • {customer}: {count} ({percentage:.1f}%)")
        
        # 分析时间分布
        years = {}
        for record in data['yearly_statistics']:
            year = record['年份']
            if year not in years:
                years[year] = 0
            years[year] += record['工程编号数量']
        
        print(f"\n📅 年度工程编号分布:")
        for year in sorted(years.keys()):
            count = years[year]
            percentage = (count / summary['总工程编号数']) * 100
            print(f"   • {year}年: {count} ({percentage:.1f}%)")
    
    print(f"\n🎯 报告功能:")
    print(f"   ✓ 数据自动处理和统计")
    print(f"   ✓ 多维度筛选功能")
    print(f"   ✓ 交互式图表展示")
    print(f"   ✓ 响应式页面设计")
    print(f"   ✓ 数据导出功能")
    
    print(f"\n💡 使用建议:")
    print(f"   • 定期更新Excel数据文件")
    print(f"   • 使用筛选功能进行深度分析")
    print(f"   • 导出特定条件下的数据进行进一步分析")
    print(f"   • 关注月度趋势变化，及时调整业务策略")
    
    print("="*60)

def main():
    """主测试函数"""
    print("🧪 客户数据统计报告系统 - 功能测试")
    print("="*50)
    
    # 测试数据完整性
    if not test_data_integrity():
        print("❌ 数据完整性测试失败")
        return
    
    print("✅ 数据完整性测试通过")
    
    # 测试报告页面
    if not test_report_page():
        print("❌ 报告页面测试失败")
        return
    
    print("✅ 报告页面测试通过")
    
    # 生成测试总结
    generate_test_summary()
    
    print("\n🎉 所有测试完成！系统运行正常。")

if __name__ == "__main__":
    main()
