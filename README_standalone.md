# 📊 客户数据统计报告 - 独立版本

这是一个完全独立的客户数据统计分析HTML页面，数据已经直接嵌入到HTML文件中，无需额外的JSON文件或服务器。

## 🌟 特点

- ✅ **完全独立** - 所有数据和代码都在一个HTML文件中
- ✅ **无需服务器** - 直接在浏览器中打开即可使用
- ✅ **数据已嵌入** - 不依赖外部JSON文件
- ✅ **功能完整** - 包含所有统计分析和可视化功能

## 🚀 使用方法

### 方法一：直接打开
直接双击 `report_standalone.html` 文件，或在浏览器中打开该文件。

### 方法二：浏览器地址栏
在浏览器地址栏输入：
```
file:///完整路径/report_standalone.html
```

## 📊 功能特性

### 📈 数据统计分析
- **月度统计**: 按客户、货号、月份分组统计工程编号数量
- **年度统计**: 按客户、货号、年份分组统计工程编号数量
- **汇总信息**: 总客户数、总货号数、总工程编号数、总订单数等

### 🎨 可视化图表
- **📈 月度趋势图**: 折线图显示工程编号数量的月度变化趋势
- **📊 年度对比图**: 柱状图显示各年度工程编号数量对比
- **🥧 客户分布图**: 饼图显示各客户的工程编号数量占比

### 🔍 交互式筛选
- **客户筛选**: 按客户简称过滤数据
- **货号筛选**: 按货号过滤数据
- **年份筛选**: 按年份过滤数据
- **月份筛选**: 按月份过滤数据
- **组合筛选**: 支持多条件组合筛选

### 📋 数据管理
- **详细数据表**: 表格形式展示统计结果
- **数据导出**: 支持将筛选后的数据导出为CSV格式
- **实时更新**: 筛选条件变化时图表和表格实时更新

## 📈 数据概况

根据嵌入的数据分析结果：
- **总客户数**: 2个（APP、GB）
- **总货号数**: 28个
- **总工程编号数**: 4764个
- **总订单数**: 116个
- **数据时间范围**: 2020-01 至 2025-08
- **客户分布**: GB占72.1%，APP占29.4%

## 🎯 使用指南

### 1. 查看汇总信息
页面顶部的卡片显示数据的总体概况。

### 2. 使用筛选功能
- 在筛选区域选择需要的筛选条件
- 点击"应用筛选"按钮更新显示
- 点击"重置筛选"按钮清除所有筛选条件

### 3. 查看图表分析
- **月度趋势图**: 观察工程编号数量的时间变化趋势
- **年度对比图**: 比较不同年份的工程编号数量
- **客户分布图**: 了解各客户的业务占比

### 4. 导出数据
- 应用所需的筛选条件
- 点击"导出数据"按钮
- 系统会下载包含当前显示数据的CSV文件

## 🛠️ 技术栈

- **HTML5 + CSS3 + JavaScript**: 纯前端实现
- **Chart.js**: 图表库
- **响应式设计**: 支持桌面端、平板、移动设备

## 📱 兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ 移动端浏览器

## 🔧 自定义

如需修改数据或样式：
1. 用文本编辑器打开 `report_standalone.html`
2. 找到 `const rawData = {` 部分修改数据
3. 修改CSS样式部分调整外观
4. 保存文件后刷新浏览器

## 💡 优势

- **便携性**: 单个文件包含所有功能
- **安全性**: 无需网络连接，数据完全本地化
- **兼容性**: 支持所有现代浏览器
- **易用性**: 双击即可使用，无需安装
- **完整性**: 包含完整的数据分析功能

## 📞 使用提示

- 建议使用现代浏览器以获得最佳体验
- 如果图表显示异常，请刷新页面
- 导出功能需要浏览器支持下载
- 筛选功能支持多条件组合使用

---

**享受数据分析的乐趣！** 🎉

这个独立版本让您可以随时随地查看和分析客户数据，无需任何额外的设置或依赖。
