# 📊 客户数据统计报告系统

一个精美的客户数据统计分析和可视化系统，支持Excel数据导入、多维度统计分析和交互式图表展示。

## 🌟 功能特性

### 📈 数据统计分析
- **月度统计**: 按客户、货号、月份分组统计工程编号数量
- **年度统计**: 按客户、货号、年份分组统计工程编号数量
- **汇总信息**: 总客户数、总货号数、总工程编号数、总订单数等

### 🎨 可视化图表
- **月度趋势图**: 折线图显示工程编号数量的月度变化趋势
- **年度对比图**: 柱状图显示各年度工程编号数量对比
- **客户分布图**: 饼图显示各客户的工程编号数量占比

### 🔍 交互式筛选
- **客户筛选**: 按客户简称过滤数据
- **货号筛选**: 按货号过滤数据
- **年份筛选**: 按年份过滤数据
- **月份筛选**: 按月份过滤数据
- **组合筛选**: 支持多条件组合筛选

### 📋 数据管理
- **详细数据表**: 表格形式展示统计结果
- **数据导出**: 支持将筛选后的数据导出为CSV格式
- **实时更新**: 筛选条件变化时图表和表格实时更新

## 📁 文件结构

```
GB_APP/
├── GB_APP.xlsx           # 原始Excel数据文件
├── data_processor.py     # 数据处理脚本
├── report.html          # HTML报告页面
├── generate_report.py   # 一键生成报告脚本
├── data.json           # 处理后的JSON数据文件
└── README.md           # 说明文档
```

## 🚀 快速开始

### 方法一：一键生成（推荐）

```bash
python generate_report.py
```

这个脚本会自动：
1. 检查并安装必要的依赖包
2. 验证所需文件是否存在
3. 处理Excel数据生成JSON文件
4. 在浏览器中打开报告页面

### 方法二：手动步骤

1. **安装依赖**
   ```bash
   pip install pandas openpyxl
   ```

2. **处理数据**
   ```bash
   python data_processor.py
   ```

3. **打开报告**
   在浏览器中打开 `report.html` 文件

## 📊 数据格式要求

Excel文件应包含以下列：
- **客户简称**: 客户的简称或代码
- **船期**: 具体的日期（支持多种日期格式）
- **订单编号**: 订单的唯一标识
- **工程编号**: 工程项目的唯一标识
- **货号**: 货物或产品的编号

示例数据：
```
客户简称 | 船期       | 订单编号 | 工程编号        | 货号
APP     | 2020-01-22 | APP65   | APP64-7199     | Han
APP     | 2020-01-22 | APP65   | APP65-0466     | Alb
```

## 🎯 使用指南

### 1. 查看汇总信息
页面顶部的卡片显示数据的总体概况，包括：
- 总客户数
- 总货号数  
- 总工程编号数
- 总订单数
- 数据记录总数

### 2. 使用筛选功能
- 在筛选区域选择需要的筛选条件
- 点击"应用筛选"按钮更新显示
- 点击"重置筛选"按钮清除所有筛选条件

### 3. 查看图表分析
- **月度趋势图**: 观察工程编号数量的时间变化趋势
- **年度对比图**: 比较不同年份的工程编号数量
- **客户分布图**: 了解各客户的业务占比

### 4. 导出数据
- 应用所需的筛选条件
- 点击"导出数据"按钮
- 系统会下载包含当前显示数据的CSV文件

## 🛠️ 技术栈

- **后端处理**: Python + Pandas
- **前端展示**: HTML5 + CSS3 + JavaScript
- **图表库**: Chart.js
- **样式设计**: 响应式设计，支持移动端

## 📱 响应式设计

报告页面采用响应式设计，支持：
- 桌面端浏览器
- 平板设备
- 移动设备

## 🔧 自定义配置

### 修改图表样式
在 `report.html` 中找到对应的图表配置，可以修改：
- 颜色主题
- 图表类型
- 动画效果
- 标签显示

### 添加新的统计维度
在 `data_processor.py` 中添加新的统计函数，然后在HTML中添加对应的图表展示。

## 📞 技术支持

如果遇到问题，请检查：
1. Excel文件格式是否正确
2. 是否安装了所需的Python包
3. 浏览器是否支持现代JavaScript特性

## 📄 许可证

本项目仅供学习和内部使用。

---

**享受数据分析的乐趣！** 🎉
